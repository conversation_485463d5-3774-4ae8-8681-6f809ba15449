[{"directory": "/Users/<USER>/Documents/workspaces/cpp/short_test", "arguments": ["/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang", "-c", "-Qunused-arguments", "-target", "arm64-apple-macos15.5", "-is<PERSON><PERSON>", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk", "-g", "-O0", "-std=c++20", "-framework", "CoreFoundation", "-isystem", "/Users/<USER>/.xmake/packages/a/abseil/20250512.0/889fe076ef8d43b887859eb235c9d726/include", "-o", "build/.objs/short_test/macosx/arm64/debug/src/main.cpp.o", "src/main.cpp"], "file": "src/main.cpp"}]